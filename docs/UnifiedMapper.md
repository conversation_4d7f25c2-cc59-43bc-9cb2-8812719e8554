# UnifiedMapper - Dokumentace

## P<PERSON>ehled

`UnifiedMapper<TSource, TTarget>` je univerz<PERSON><PERSON>í mapper, který poskytuje kompletní řešení pro mapování mezi dvěma typy objektů. Kombinuje automatické mapování, konfigurovatelné mapování a obousměrné operace v jedné třídě.

## Klíčové vlastnosti

- **Automatické mapování** - mapuje vlastnosti se stejnými názvy
- **Konfigurovatelné mapování** - umožňuje vlastní mapovací logiku
- **Obousměrné operace** - mapování v obou směrech (TSource ↔ TTarget)
- **Aktualizace existujících instancí** - bez vytváření nových objektů
- **Mapování kolekcí** - hromadné operace
- **Optimalizace výkonu** - použití Expression Trees a lazy loading
- **Thread-safe** - bezpečné pro použití v multi-threaded prostředí

## Rozhraní

### IUnifiedMapper<TSource, TTarget>

```csharp
public interface IUnifiedMapper<TSource, TTarget>
    where TSource : class
    where TTarget : class
{
    // Základní mapování
    TTarget Map(TSource source, bool useConfig = false);
    TSource MapBack(TTarget target, bool useConfig = false);
    
    // Aktualizace existujících instancí
    void Update(TSource source, TTarget target);
    void UpdateBack(TTarget target, TSource source);
    
    // Mapování kolekcí
    IEnumerable<TTarget> MapCollection(IEnumerable<TSource> sources, bool useConfig = false);
    IEnumerable<TSource> MapBackCollection(IEnumerable<TTarget> targets, bool useConfig = false);
}
```

### IMappingConfig<TSource, TTarget>

```csharp
public interface IMappingConfig<TSource, TTarget>
    where TSource : class
    where TTarget : class
{
    void Map<TPropertySource, TPropertyTarget>(
        Expression<Func<TSource, TPropertySource>> sourceExpression,
        Expression<Func<TTarget, TPropertyTarget>> targetExpression,
        Func<TPropertySource, TPropertyTarget>? converter = null);
}
```

## Základní použití

### 1. Automatické mapování

```csharp
// Vytvoření mapperu bez konfigurace
var mapper = new UnifiedMapper<SampleAddEdit, SampleEntity>();

// Mapování DTO na entitu
var addEditDto = new SampleAddEdit
{
    Name = "Test Sample",
    Description = "Test Description",
    Age = 25,
    IsActive = true
};

var entity = mapper.Map(addEditDto);
// Automaticky mapuje všechny vlastnosti se stejnými názvy

// Zpětné mapování entity na DTO
var backToDto = mapper.MapBack(entity);
```

### 2. Mapování kolekcí

```csharp
var mapper = new UnifiedMapper<SampleEntity, SampleDto>();

// Mapování kolekce entit na DTO
List<SampleEntity> entities = GetEntitiesFromDatabase();
var dtos = mapper.MapCollection(entities).ToList();

// Zpětné mapování kolekce DTO na entity
var backToEntities = mapper.MapBackCollection(dtos).ToList();
```

### 3. Aktualizace existujících instancí

```csharp
var mapper = new UnifiedMapper<SampleAddEdit, SampleEntity>();

// Existující entita z databáze
var existingEntity = await dbContext.SampleEntities.FindAsync(id);

// DTO s novými hodnotami
var updateDto = new SampleAddEdit
{
    Name = "Updated Name",
    Description = "Updated Description",
    Age = 30
};

// Aktualizace existující entity bez vytvoření nové instance
mapper.Update(updateDto, existingEntity);
// existingEntity nyní obsahuje aktualizované hodnoty
```

## Konfigurovatelné mapování

### Základní konfigurace

```csharp
var mapper = new UnifiedMapper<OrderDto, OrderEntity>(
    // Konfigurace pro mapování OrderDto -> OrderEntity
    forwardConfig: config =>
    {
        // Mapování s konverzí
        config.Map(
            source => source.TotalAmountString,
            target => target.TotalAmount,
            converter: str => decimal.Parse(str)
        );
        
        // Mapování s výpočtem
        config.Map(
            source => source.CustomerName,
            target => target.CustomerDisplayName,
            converter: name => $"Zákazník: {name}"
        );
    },
    
    // Konfigurace pro zpětné mapování OrderEntity -> OrderDto
    reverseConfig: config =>
    {
        config.Map(
            source => source.TotalAmount,
            target => target.TotalAmountString,
            converter: amount => amount.ToString("C")
        );
        
        config.Map(
            source => source.CustomerDisplayName,
            target => target.CustomerName,
            converter: displayName => displayName.Replace("Zákazník: ", "")
        );
    }
);

// Použití konfigurovaného mapování
var orderDto = new OrderDto { TotalAmountString = "1500.50", CustomerName = "Jan Novák" };
var orderEntity = mapper.Map(orderDto, useConfig: true);
```

### Komplexní mapování s transformacemi

```csharp
var mapper = new UnifiedMapper<InvoiceDto, InvoiceEntity>(
    forwardConfig: config =>
    {
        // Mapování data s formátováním
        config.Map(
            source => source.IssueDateString,
            target => target.IssueDate,
            converter: dateStr => DateTime.ParseExact(dateStr, "dd.MM.yyyy", null)
        );
        
        // Mapování enum hodnot
        config.Map(
            source => source.StatusText,
            target => target.Status,
            converter: text => Enum.Parse<InvoiceStatus>(text)
        );
        
        // Výpočet odvozených hodnot
        config.Map(
            source => source.SubTotal,
            target => target.TotalAmount,
            converter: subTotal => subTotal * 1.21m // Přidání DPH
        );
    }
);
```

## Pokročilé použití

### Registrace v Dependency Injection

```csharp
// V Infrastructure/DependencyInjection.cs
services.AddSingleton<IUnifiedMapper<SampleEntity, SampleDto>>(_ => 
    new UnifiedMapper<SampleEntity, SampleDto>());

services.AddSingleton<IUnifiedMapper<SampleAddEdit, SampleEntity>>(_ => 
    new UnifiedMapper<SampleAddEdit, SampleEntity>());

// Použití v service třídě
public class SampleService
{
    private readonly IUnifiedMapper<SampleEntity, SampleDto> _entityToDto;
    private readonly IUnifiedMapper<SampleAddEdit, SampleEntity> _addEditToEntity;
    
    public SampleService(
        IUnifiedMapper<SampleEntity, SampleDto> entityToDto,
        IUnifiedMapper<SampleAddEdit, SampleEntity> addEditToEntity)
    {
        _entityToDto = entityToDto;
        _addEditToEntity = addEditToEntity;
    }
    
    public async Task<SampleDto> CreateSampleAsync(SampleAddEdit addEdit)
    {
        var entity = _addEditToEntity.Map(addEdit);
        await _dbContext.SampleEntities.AddAsync(entity);
        await _dbContext.SaveChangesAsync();
        
        return _entityToDto.Map(entity);
    }
}
```

### Kombinace s Entity Framework

```csharp
public class OrderService
{
    private readonly ApplicationDbContext _context;
    private readonly IUnifiedMapper<Order, OrderDto> _mapper;
    
    public async Task<List<OrderDto>> GetOrdersAsync()
    {
        var orders = await _context.Orders
            .Include(o => o.OrderItems)
            .ToListAsync();
            
        return _mapper.MapCollection(orders).ToList();
    }
    
    public async Task UpdateOrderAsync(int orderId, OrderAddEdit updateDto)
    {
        var existingOrder = await _context.Orders.FindAsync(orderId);
        if (existingOrder == null) return;
        
        // Aktualizace bez vytvoření nové instance
        var updateMapper = new UnifiedMapper<OrderAddEdit, Order>();
        updateMapper.Update(updateDto, existingOrder);
        
        await _context.SaveChangesAsync();
    }
}
```

## Speciální funkce

### Automatické zpracování RowVersion

Mapper automaticky zpracovává `RowVersion` vlastnost pro optimistické zamykání:

```csharp
// Při mapování na entity se automaticky nastaví výchozí RowVersion
var entity = mapper.Map(dto); // entity.RowVersion = [0,0,0,0,0,0,0,1]

// Při aktualizaci se RowVersion přeskakuje (aktualizuje se automaticky v DbContext)
mapper.Update(dto, existingEntity); // RowVersion zůstává nezměněn
```

### Výkonnostní optimalizace

- **Lazy loading** - mapovací funkce se kompilují pouze při prvním použití
- **Expression Trees** - vysoký výkon díky kompilovaným expressions
- **Thread-safe** - bezpečné pro concurrent použití
- **Caching** - zkompilované mapovací funkce se ukládají staticky

## Příklady chybových stavů

### Null hodnoty

```csharp
var mapper = new UnifiedMapper<SampleDto, SampleEntity>();

// Vyvolá ArgumentNullException
try
{
    var result = mapper.Map(null);
}
catch (ArgumentNullException ex)
{
    Console.WriteLine($"Chyba: {ex.Message}");
}
```

### Nekompatibilní typy v konfiguraci

```csharp
var mapper = new UnifiedMapper<OrderDto, OrderEntity>(
    forwardConfig: config =>
    {
        // Chyba - nekompatibilní typy bez konverteru
        config.Map(
            source => source.TotalAmountString, // string
            target => target.TotalAmount        // decimal
            // Chybí converter!
        );
    }
);

// Vyvolá runtime exception při pokusu o mapování
```

## Nejlepší praktiky

1. **Používejte DI kontejner** pro registraci mapperů
2. **Definujte konfigurace jednou** při registraci
3. **Testujte mapování** zejména při použití konverterů
4. **Používejte Update metody** pro aktualizace existujících entit
5. **Kombinujte s Entity Framework** pro optimální výkon
6. **Ošetřujte null hodnoty** v konverterech

## Testování

```csharp
[Test]
public void Map_SampleAddEditToEntity_ShouldMapAllProperties()
{
    // Arrange
    var mapper = new UnifiedMapper<SampleAddEdit, SampleEntity>();
    var addEdit = new SampleAddEdit
    {
        Name = "Test",
        Description = "Test Description",
        Age = 25,
        IsActive = true
    };

    // Act
    var entity = mapper.Map(addEdit);

    // Assert
    Assert.Equal(addEdit.Name, entity.Name);
    Assert.Equal(addEdit.Description, entity.Description);
    Assert.Equal(addEdit.Age, entity.Age);
    Assert.Equal(addEdit.IsActive, entity.IsActive);
    Assert.NotNull(entity.RowVersion);
}

[Test]
public void MapBack_EntityToDto_ShouldIncludeTrackingProperties()
{
    // Arrange
    var mapper = new UnifiedMapper<SampleEntity, SampleDto>();
    var entity = new SampleEntity
    {
        Id = 1,
        Name = "Test Entity",
        CreatedAt = DateTime.Now,
        CreatedBy = "TestUser",
        ModifiedAt = DateTime.Now,
        ModifiedBy = "TestUser"
    };

    // Act
    var dto = mapper.MapBack(entity);

    // Assert
    Assert.Equal(entity.Id, dto.Id);
    Assert.Equal(entity.Name, dto.Name);
    Assert.Equal(entity.CreatedAt, dto.CreatedAt);
    Assert.Equal(entity.CreatedBy, dto.CreatedBy);
}

[Test]
public void Update_ExistingEntity_ShouldPreserveId()
{
    // Arrange
    var mapper = new UnifiedMapper<SampleAddEdit, SampleEntity>();
    var existingEntity = new SampleEntity { Id = 5, Name = "Original" };
    var updateDto = new SampleAddEdit { Name = "Updated" };

    // Act
    mapper.Update(updateDto, existingEntity);

    // Assert
    Assert.Equal(5, existingEntity.Id); // ID zůstává nezměněno
    Assert.Equal("Updated", existingEntity.Name); // Název se aktualizuje
}

[Test]
public void MapCollection_EmptyCollection_ShouldReturnEmpty()
{
    // Arrange
    var mapper = new UnifiedMapper<SampleEntity, SampleDto>();
    var emptyList = new List<SampleEntity>();

    // Act
    var result = mapper.MapCollection(emptyList);

    // Assert
    Assert.Empty(result);
}
```

## Pokročilé scénáře

### Mapování s podmínkami

```csharp
var mapper = new UnifiedMapper<UserDto, UserEntity>(
    forwardConfig: config =>
    {
        // Podmíněné mapování - nastaví email pouze pokud je validní
        config.Map(
            source => source.EmailAddress,
            target => target.Email,
            converter: email => IsValidEmail(email) ? email : null
        );

        // Mapování s výchozí hodnotou
        config.Map(
            source => source.Age,
            target => target.Age,
            converter: age => age ?? 0 // Výchozí věk 0 pokud není zadán
        );
    }
);

private static bool IsValidEmail(string email)
{
    return !string.IsNullOrEmpty(email) && email.Contains("@");
}
```

### Mapování vnořených objektů

```csharp
var mapper = new UnifiedMapper<OrderDto, OrderEntity>(
    forwardConfig: config =>
    {
        // Mapování vnořeného objektu
        config.Map(
            source => source.Customer,
            target => target.CustomerId,
            converter: customer => customer?.Id ?? 0
        );

        // Mapování kolekce položek
        config.Map(
            source => source.Items,
            target => target.OrderItems,
            converter: items => items?.Select(MapOrderItem).ToList() ?? new List<OrderItemEntity>()
        );
    }
);

private static OrderItemEntity MapOrderItem(OrderItemDto dto)
{
    var itemMapper = new UnifiedMapper<OrderItemDto, OrderItemEntity>();
    return itemMapper.Map(dto);
}
```

### Mapování s validací

```csharp
var mapper = new UnifiedMapper<ProductDto, ProductEntity>(
    forwardConfig: config =>
    {
        // Mapování s validací
        config.Map(
            source => source.Price,
            target => target.Price,
            converter: price =>
            {
                if (price < 0)
                    throw new ArgumentException("Cena nemůže být záporná");
                return price;
            }
        );

        // Mapování s normalizací
        config.Map(
            source => source.Name,
            target => target.Name,
            converter: name => name?.Trim().ToUpperInvariant()
        );
    }
);
```

## Integrace s dalšími systémy

### Použití s AutoMapper (migrace)

```csharp
// Původní AutoMapper konfigurace
public class AutoMapperProfile : Profile
{
    public AutoMapperProfile()
    {
        CreateMap<SampleEntity, SampleDto>();
        CreateMap<SampleAddEdit, SampleEntity>();
    }
}

// Migrace na UnifiedMapper
public static class MapperFactory
{
    public static IUnifiedMapper<SampleEntity, SampleDto> CreateEntityToDtoMapper()
    {
        return new UnifiedMapper<SampleEntity, SampleDto>();
    }

    public static IUnifiedMapper<SampleAddEdit, SampleEntity> CreateAddEditToEntityMapper()
    {
        return new UnifiedMapper<SampleAddEdit, SampleEntity>();
    }
}
```

### Použití s MediatR

```csharp
public class CreateSampleCommandHandler : IRequestHandler<CreateSampleCommand, Result<int>>
{
    private readonly IApplicationDbContext _context;
    private readonly IUnifiedMapper<SampleAddEdit, SampleEntity> _mapper;

    public CreateSampleCommandHandler(
        IApplicationDbContext context,
        IUnifiedMapper<SampleAddEdit, SampleEntity> mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<Result<int>> Handle(CreateSampleCommand request, CancellationToken cancellationToken)
    {
        var entity = _mapper.Map(request.Payload);

        _context.SampleEntities.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);

        return Result<int>.Success(entity.Id);
    }
}
```

## Troubleshooting

### Časté problémy a řešení

**1. Chyba: "Property setter not found"**
```csharp
// Problém: Vlastnost nemá setter
public class ReadOnlyDto
{
    public string Name { get; } // Pouze getter
}

// Řešení: Přidejte setter nebo použijte konfigurované mapování
public class ReadOnlyDto
{
    public string Name { get; set; } // Getter i setter
}
```

**2. Chyba: "Type conversion failed"**
```csharp
// Problém: Nekompatibilní typy
config.Map(
    source => source.DateString, // string
    target => target.Date        // DateTime
    // Chybí converter
);

// Řešení: Přidejte converter
config.Map(
    source => source.DateString,
    target => target.Date,
    converter: str => DateTime.Parse(str)
);
```

**3. Výkonnostní problémy**
```csharp
// Problém: Vytváření nového mapperu v každé iteraci
foreach (var item in items)
{
    var mapper = new UnifiedMapper<Source, Target>(); // Špatně!
    var result = mapper.Map(item);
}

// Řešení: Vytvořte mapper jednou
var mapper = new UnifiedMapper<Source, Target>();
foreach (var item in items)
{
    var result = mapper.Map(item); // Správně!
}
```

## Závěr

UnifiedMapper poskytuje kompletní řešení pro mapování objektů v .NET aplikacích s důrazem na výkon, flexibilitu a jednoduchost použití. Kombinuje automatické mapování pro běžné scénáře s možností detailní konfigurace pro složitější případy použití.
